from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
import os

# Try to import PIL, but don't fail if it's not available
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

User = get_user_model()


def user_profile_image_path(instance, filename):
    """Generate file path for user profile images"""
    return f'profiles/{instance.user.username}/{filename}'


def user_cover_image_path(instance, filename):
    """Generate file path for user cover images"""
    return f'profiles/{instance.user.username}/cover/{filename}'


class Profile(models.Model):
    """User profile model"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(_('bio'), max_length=500, blank=True)
    location = models.CharField(_('location'), max_length=100, blank=True)
    website = models.URLField(_('website'), blank=True)
    profile_image = models.ImageField(
        _('profile image'),
        upload_to=user_profile_image_path,
        default='profiles/default.jpg',
        blank=True
    )
    cover_image = models.ImageField(
        _('cover image'),
        upload_to=user_cover_image_path,
        blank=True,
        null=True
    )
    is_private = models.BooleanField(_('private account'), default=False)
    followers_count = models.PositiveIntegerField(_('followers count'), default=0)
    following_count = models.PositiveIntegerField(_('following count'), default=0)
    posts_count = models.PositiveIntegerField(_('posts count'), default=0)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Profile')
        verbose_name_plural = _('Profiles')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.get_full_name()}'s Profile"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Resize profile image if it exists and PIL is available
        if PIL_AVAILABLE and self.profile_image and hasattr(self.profile_image, 'path'):
            try:
                img = Image.open(self.profile_image.path)
                if img.height > 300 or img.width > 300:
                    output_size = (300, 300)
                    img.thumbnail(output_size)
                    img.save(self.profile_image.path)
            except Exception:
                pass

        # Resize cover image if it exists and PIL is available
        if PIL_AVAILABLE and self.cover_image and hasattr(self.cover_image, 'path'):
            try:
                img = Image.open(self.cover_image.path)
                if img.height > 400 or img.width > 1200:
                    output_size = (1200, 400)
                    img.thumbnail(output_size)
                    img.save(self.cover_image.path)
            except Exception:
                pass
    
    def get_absolute_url(self):
        return f'/profile/{self.user.username}/'
    
    def get_followers(self):
        """Get all followers of this profile"""
        from social.models import Follow
        return Follow.objects.filter(following=self.user).select_related('follower')
    
    def get_following(self):
        """Get all users this profile is following"""
        from social.models import Follow
        return Follow.objects.filter(follower=self.user).select_related('following')


# Signal to create profile when user is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a profile when a new user is created"""
    if created:
        Profile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Save the profile when the user is saved"""
    if hasattr(instance, 'profile'):
        instance.profile.save()
