from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth import get_user_model
from django.contrib import messages
from django.views.generic import UpdateView
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.db.models import Q

from .models import Profile
from .forms import ProfileUpdateForm
from posts.models import Post
from social.models import Follow

User = get_user_model()


def profile_view(request, username):
    """View user profile"""
    user = get_object_or_404(User, username=username)
    profile = user.profile
    
    # Check if current user is following this profile
    is_following = False
    if request.user.is_authenticated:
        is_following = Follow.objects.filter(
            follower=request.user, 
            following=user
        ).exists()
    
    # Get user's posts
    posts = Post.objects.filter(author=user).order_by('-created_at')
    
    # If profile is private and user is not following, show limited info
    if profile.is_private and not is_following and request.user != user:
        posts = Post.objects.none()
    
    # Pagination
    paginator = Paginator(posts, 10)
    page_number = request.GET.get('page')
    posts = paginator.get_page(page_number)
    
    context = {
        'profile_user': user,
        'profile': profile,
        'posts': posts,
        'is_following': is_following,
        'is_own_profile': request.user == user,
        'followers_count': profile.followers_count,
        'following_count': profile.following_count,
        'posts_count': profile.posts_count,
    }
    
    return render(request, 'profiles/profile.html', context)


@login_required
def my_profile(request):
    """Redirect to current user's profile"""
    return redirect('profiles:profile', username=request.user.username)


class ProfileUpdateView(LoginRequiredMixin, UpdateView):
    """View for updating user profile"""
    model = Profile
    form_class = ProfileUpdateForm
    template_name = 'profiles/profile_edit.html'
    
    def get_object(self):
        return self.request.user.profile
    
    def get_success_url(self):
        return reverse_lazy('profiles:profile', kwargs={'username': self.request.user.username})
    
    def form_valid(self, form):
        messages.success(self.request, _('Profile updated successfully!'))
        return super().form_valid(form)


@login_required
def followers_list(request, username):
    """View list of followers"""
    user = get_object_or_404(User, username=username)
    profile = user.profile
    
    # Check privacy
    if profile.is_private and request.user != user:
        is_following = Follow.objects.filter(
            follower=request.user, 
            following=user
        ).exists()
        if not is_following:
            messages.error(request, _('This account is private.'))
            return redirect('profiles:profile', username=username)
    
    followers = profile.get_followers()
    
    # Pagination
    paginator = Paginator(followers, 20)
    page_number = request.GET.get('page')
    followers = paginator.get_page(page_number)
    
    context = {
        'profile_user': user,
        'followers': followers,
        'title': _('Followers')
    }
    
    return render(request, 'profiles/followers_list.html', context)


@login_required
def following_list(request, username):
    """View list of following"""
    user = get_object_or_404(User, username=username)
    profile = user.profile
    
    # Check privacy
    if profile.is_private and request.user != user:
        is_following = Follow.objects.filter(
            follower=request.user, 
            following=user
        ).exists()
        if not is_following:
            messages.error(request, _('This account is private.'))
            return redirect('profiles:profile', username=username)
    
    following = profile.get_following()
    
    # Pagination
    paginator = Paginator(following, 20)
    page_number = request.GET.get('page')
    following = paginator.get_page(page_number)
    
    context = {
        'profile_user': user,
        'following': following,
        'title': _('Following')
    }
    
    return render(request, 'profiles/following_list.html', context)
