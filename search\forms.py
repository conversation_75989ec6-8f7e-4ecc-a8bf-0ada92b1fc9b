from django import forms
from django.utils.translation import gettext_lazy as _


class SearchForm(forms.Form):
    """Main search form"""
    
    SEARCH_TYPES = [
        ('all', _('All')),
        ('users', _('Users')),
        ('posts', _('Posts')),
    ]
    
    query = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search for users, posts...'),
            'required': True
        }),
        label=_('Search')
    )
    
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPES,
        initial='all',
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label=_('Search Type'),
        required=False
    )
    
    def clean_query(self):
        query = self.cleaned_data.get('query')
        if query:
            query = query.strip()
            if len(query) < 2:
                raise forms.ValidationError(_('Search query must be at least 2 characters long.'))
        return query


class AdvancedSearchForm(forms.Form):
    """Advanced search form with filters"""
    
    query = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search query'),
            'required': True
        }),
        label=_('Search Query')
    )
    
    # User filters
    location = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Location')
        }),
        label=_('Location')
    )
    
    # Post filters
    has_image = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label=_('Has Image')
    )
    
    has_video = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label=_('Has Video')
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label=_('From Date')
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label=_('To Date')
    )
    
    def clean_query(self):
        query = self.cleaned_data.get('query')
        if query:
            query = query.strip()
            if len(query) < 2:
                raise forms.ValidationError(_('Search query must be at least 2 characters long.'))
        return query
    
    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise forms.ValidationError(_('From date cannot be later than to date.'))
        
        return cleaned_data
