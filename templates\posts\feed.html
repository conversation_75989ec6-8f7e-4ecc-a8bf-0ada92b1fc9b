{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load humanize %}

{% block title %}{% trans 'Feed' %} - عثتر{% endblock %}

{% block content %}

<!-- Create Post Form -->
<div class="card post-card mb-4">
    <div class="card-body">
        <div class="d-flex align-items-start">
            <img src="{{ user.profile.profile_image.url }}" alt="{{ user.get_full_name }}" 
                 class="rounded-circle me-3" width="50" height="50">
            <div class="flex-grow-1">
                <form method="post" action="{% url 'posts:create' %}" enctype="multipart/form-data" id="create-post-form">
                    {% csrf_token %}
                    <div class="mb-3">
                        {{ form.content }}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.image.id_for_label }}" class="form-label small">
                                <i class="fas fa-image text-yemeni-primary"></i> {{ form.image.label }}
                            </label>
                            {{ form.image }}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.video.id_for_label }}" class="form-label small">
                                <i class="fas fa-video text-yemeni-primary"></i> {{ form.video.label }}
                            </label>
                            {{ form.video }}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="post-options">
                            <span class="text-muted small">
                                <i class="fas fa-globe-americas"></i> {% trans 'Public' %}
                            </span>
                        </div>
                        <button type="submit" class="btn btn-yemeni-primary">
                            <i class="fas fa-paper-plane"></i> {% trans 'Post' %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Posts Feed -->
<div class="posts-container">
    {% for post in posts %}
        {% include 'posts/includes/post_card.html' %}
    {% empty %}
        <div class="card text-center py-5">
            <div class="card-body">
                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                <h5>{% trans 'No posts yet' %}</h5>
                <p class="text-muted">
                    {% trans 'Follow some users or create your first post to see content here.' %}
                </p>
                <a href="{% url 'social:suggested_users' %}" class="btn btn-outline-yemeni-primary">
                    {% trans 'Find People to Follow' %}
                </a>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if posts.has_other_pages %}
    <nav aria-label="{% trans 'Posts pagination' %}">
        <ul class="pagination justify-content-center">
            {% if posts.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ posts.previous_page_number }}">
                        {% trans 'Previous' %}
                    </a>
                </li>
            {% endif %}
            
            {% for num in posts.paginator.page_range %}
                {% if posts.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > posts.number|add:'-3' and num < posts.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if posts.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ posts.next_page_number }}">
                        {% trans 'Next' %}
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
    // Auto-expand textarea
    $('#id_content').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Character counter
    $('#id_content').on('input', function() {
        const maxLength = 2000;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;
        
        let counter = $('.char-counter');
        if (counter.length === 0) {
            counter = $('<div class="char-counter text-muted small mt-1"></div>');
            $(this).after(counter);
        }
        
        counter.text(`${remaining} حرف متبقي`);
        
        if (remaining < 100) {
            counter.removeClass('text-muted').addClass('text-warning');
        } else if (remaining < 0) {
            counter.removeClass('text-warning').addClass('text-danger');
        } else {
            counter.removeClass('text-warning text-danger').addClass('text-muted');
        }
    });
</script>
{% endblock %}
