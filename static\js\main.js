// Athtar - Main JavaScript File

$(document).ready(function() {
    
    // CSRF Token Setup
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    const csrftoken = getCookie('csrftoken');
    
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!(/^http:.*/.test(settings.url) || /^https:.*/.test(settings.url))) {
                xhr.setRequestHeader("X-CSRFToken", csrftoken);
            }
        }
    });
    
    // Like/Unlike Posts
    $(document).on('click', '.like-btn', function(e) {
        e.preventDefault();
        const btn = $(this);
        const postId = btn.data('post-id');
        const icon = btn.find('i');
        const countSpan = btn.find('.like-count');
        
        $.post(`/post/${postId}/like/`, function(data) {
            if (data.liked) {
                icon.removeClass('far').addClass('fas');
                btn.addClass('liked');
            } else {
                icon.removeClass('fas').addClass('far');
                btn.removeClass('liked');
            }
            countSpan.text(data.likes_count);
        }).fail(function() {
            alert('حدث خطأ. يرجى المحاولة مرة أخرى.');
        });
    });
    
    // Like/Unlike Comments
    $(document).on('click', '.comment-like-btn', function(e) {
        e.preventDefault();
        const btn = $(this);
        const commentId = btn.data('comment-id');
        const icon = btn.find('i');
        const countSpan = btn.find('.like-count');
        
        $.post(`/comment/${commentId}/like/`, function(data) {
            if (data.liked) {
                icon.removeClass('far').addClass('fas');
                btn.addClass('liked');
            } else {
                icon.removeClass('fas').addClass('far');
                btn.removeClass('liked');
            }
            countSpan.text(data.likes_count);
        }).fail(function() {
            alert('حدث خطأ. يرجى المحاولة مرة أخرى.');
        });
    });
    
    // Follow/Unfollow Users
    $(document).on('click', '.follow-btn', function(e) {
        e.preventDefault();
        const btn = $(this);
        const username = btn.data('username');
        
        $.post(`/social/follow/${username}/`, function(data) {
            if (data.is_following) {
                btn.removeClass('btn-outline-yemeni-primary').addClass('btn-yemeni-primary');
                btn.text('إلغاء المتابعة');
            } else {
                if (data.action === 'requested') {
                    btn.removeClass('btn-outline-yemeni-primary').addClass('btn-secondary');
                    btn.text('تم إرسال الطلب');
                } else {
                    btn.removeClass('btn-yemeni-primary').addClass('btn-outline-yemeni-primary');
                    btn.text('متابعة');
                }
            }
            
            // Update followers count if element exists
            const followersCount = $('.followers-count');
            if (followersCount.length) {
                followersCount.text(data.followers_count);
            }
        }).fail(function() {
            alert('حدث خطأ. يرجى المحاولة مرة أخرى.');
        });
    });
    
    // Share Posts
    $(document).on('click', '.share-btn', function(e) {
        e.preventDefault();
        const btn = $(this);
        const postId = btn.data('post-id');
        const countSpan = btn.find('.share-count');
        
        $.post(`/post/${postId}/share/`, function(data) {
            if (data.shared) {
                btn.addClass('shared');
                alert('تم مشاركة المنشور بنجاح!');
            } else {
                alert('لقد قمت بمشاركة هذا المنشور من قبل.');
            }
            countSpan.text(data.shares_count);
        }).fail(function() {
            alert('حدث خطأ. يرجى المحاولة مرة أخرى.');
        });
    });
    
    // Auto-resize textareas
    $(document).on('input', 'textarea', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Character counter for post content
    $(document).on('input', '#id_content', function() {
        const maxLength = 2000;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;
        
        let counter = $('.char-counter');
        if (counter.length === 0) {
            counter = $('<small class="char-counter text-muted"></small>');
            $(this).after(counter);
        }
        
        counter.text(`${remaining} حرف متبقي`);
        
        if (remaining < 100) {
            counter.removeClass('text-muted').addClass('text-warning');
        } else if (remaining < 0) {
            counter.removeClass('text-warning').addClass('text-danger');
        } else {
            counter.removeClass('text-warning text-danger').addClass('text-muted');
        }
    });
    
    // Image preview for file uploads
    $(document).on('change', 'input[type="file"]', function() {
        const input = this;
        const preview = $(input).siblings('.image-preview');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                if (preview.length === 0) {
                    $(input).after('<div class="image-preview mt-2"><img src="" class="img-fluid rounded" style="max-height: 200px;"></div>');
                }
                $(input).siblings('.image-preview').find('img').attr('src', e.target.result);
            };
            
            reader.readAsDataURL(input.files[0]);
        }
    });
    
    // Infinite scroll for feeds
    let loading = false;
    $(window).scroll(function() {
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 100) {
            if (!loading) {
                const nextPageUrl = $('.pagination .page-item:last-child .page-link').attr('href');
                if (nextPageUrl && nextPageUrl !== '#') {
                    loading = true;
                    loadMorePosts(nextPageUrl);
                }
            }
        }
    });
    
    function loadMorePosts(url) {
        $.get(url, function(data) {
            const newPosts = $(data).find('.post-card');
            $('.posts-container').append(newPosts);
            
            // Update pagination
            const newPagination = $(data).find('.pagination');
            $('.pagination').replaceWith(newPagination);
            
            loading = false;
        }).fail(function() {
            loading = false;
        });
    }
    
    // Real-time notifications (placeholder)
    function checkNotifications() {
        // This would connect to WebSocket or poll for notifications
        // For now, just a placeholder
    }
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Format hashtags and mentions in post content
    function formatPostContent(content) {
        // Format hashtags
        content = content.replace(/#(\w+)/g, '<a href="/search/hashtag/$1/" class="hashtag">#$1</a>');
        
        // Format mentions
        content = content.replace(/@(\w+)/g, '<a href="/profile/$1/" class="mention">@$1</a>');
        
        return content;
    }
    
    // Apply formatting to existing content
    $('.post-content').each(function() {
        const content = $(this).html();
        $(this).html(formatPostContent(content));
    });
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
    
});

// WebSocket for real-time messaging (if on conversation page)
if (window.location.pathname.includes('/messages/')) {
    const conversationId = window.location.pathname.split('/')[2];
    if (conversationId && !isNaN(conversationId)) {
        const chatSocket = new WebSocket(
            'ws://' + window.location.host + '/ws/chat/' + conversationId + '/'
        );
        
        chatSocket.onmessage = function(e) {
            const data = JSON.parse(e.data);
            const messagesContainer = $('.messages-container');
            
            const messageHtml = `
                <div class="message-bubble ${data.sender_id == currentUserId ? 'sent' : 'received'}">
                    <div class="message-content">${data.message}</div>
                    <small class="message-time">${data.timestamp}</small>
                </div>
            `;
            
            messagesContainer.append(messageHtml);
            messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
        };
        
        chatSocket.onclose = function(e) {
            console.error('Chat socket closed unexpectedly');
        };
        
        // Send message
        $('#message-form').on('submit', function(e) {
            e.preventDefault();
            const messageInput = $('#message-input');
            const message = messageInput.val().trim();
            
            if (message) {
                chatSocket.send(JSON.stringify({
                    'message': message
                }));
                messageInput.val('');
            }
        });
    }
}
