from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Profile


class ProfileUpdateForm(forms.ModelForm):
    """Form for updating user profile"""
    
    class Meta:
        model = Profile
        fields = ('bio', 'location', 'website', 'profile_image', 'cover_image', 'is_private')
        widgets = {
            'bio': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': _('Tell us about yourself...')
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Your location')
            }),
            'website': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': _('Your website URL'),
                'dir': 'ltr'
            }),
            'profile_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'cover_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'is_private': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }
        labels = {
            'bio': _('Bio'),
            'location': _('Location'),
            'website': _('Website'),
            'profile_image': _('Profile Image'),
            'cover_image': _('Cover Image'),
            'is_private': _('Private Account')
        }
        help_texts = {
            'bio': _('Maximum 500 characters'),
            'is_private': _('Only approved followers can see your posts'),
            'profile_image': _('Recommended size: 300x300 pixels'),
            'cover_image': _('Recommended size: 1200x400 pixels')
        }
    
    def clean_profile_image(self):
        image = self.cleaned_data.get('profile_image')
        if image:
            if image.size > 5 * 1024 * 1024:  # 5MB
                raise forms.ValidationError(_('Image file too large ( > 5MB )'))
        return image
    
    def clean_cover_image(self):
        image = self.cleaned_data.get('cover_image')
        if image:
            if image.size > 10 * 1024 * 1024:  # 10MB
                raise forms.ValidationError(_('Image file too large ( > 10MB )'))
        return image
