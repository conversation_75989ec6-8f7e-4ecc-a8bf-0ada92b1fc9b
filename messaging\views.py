from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.db.models import Q, Max
from django.http import JsonResponse
from django.views.decorators.http import require_POST

from .models import Conversation, Message
from .forms import MessageForm, NewConversationForm
from social.models import Block

User = get_user_model()


@login_required
def inbox(request):
    """View user's message inbox"""
    conversations = Conversation.objects.filter(
        participants=request.user
    ).annotate(
        last_message_time=Max('messages__created_at')
    ).order_by('-last_message_time')
    
    # Pagination
    paginator = Paginator(conversations, 20)
    page_number = request.GET.get('page')
    conversations = paginator.get_page(page_number)
    
    context = {
        'conversations': conversations,
        'title': _('Messages')
    }
    
    return render(request, 'messaging/inbox.html', context)


@login_required
def conversation_detail(request, conversation_id):
    """View conversation details and messages"""
    conversation = get_object_or_404(
        Conversation,
        id=conversation_id,
        participants=request.user
    )
    
    # Mark messages as read
    unread_messages = conversation.messages.filter(is_read=False).exclude(sender=request.user)
    unread_messages.update(is_read=True)
    
    # Get messages
    messages_list = conversation.messages.select_related('sender').order_by('created_at')
    
    # Pagination
    paginator = Paginator(messages_list, 50)
    page_number = request.GET.get('page')
    messages_page = paginator.get_page(page_number)
    
    # Message form
    if request.method == 'POST':
        form = MessageForm(request.POST)
        if form.is_valid():
            message = form.save(commit=False)
            message.conversation = conversation
            message.sender = request.user
            message.save()
            messages.success(request, _('Message sent successfully!'))
            return redirect('messaging:conversation', conversation_id=conversation.id)
    else:
        form = MessageForm()
    
    # Get other participant
    other_participant = conversation.get_other_participant(request.user)
    
    context = {
        'conversation': conversation,
        'messages': messages_page,
        'form': form,
        'other_participant': other_participant,
        'title': _('Conversation with {}').format(other_participant.get_full_name() if other_participant else _('Unknown'))
    }
    
    return render(request, 'messaging/conversation.html', context)


@login_required
def new_conversation(request):
    """Start a new conversation"""
    if request.method == 'POST':
        form = NewConversationForm(request.POST, user=request.user)
        if form.is_valid():
            recipient = form.cleaned_data['recipient']
            message_content = form.cleaned_data['message']
            
            # Check if conversation already exists
            existing_conversation = Conversation.objects.filter(
                participants=request.user
            ).filter(
                participants=recipient
            ).filter(
                participants__count=2
            ).first()
            
            if existing_conversation:
                # Add message to existing conversation
                Message.objects.create(
                    conversation=existing_conversation,
                    sender=request.user,
                    content=message_content
                )
                messages.success(request, _('Message sent successfully!'))
                return redirect('messaging:conversation', conversation_id=existing_conversation.id)
            else:
                # Create new conversation
                conversation = Conversation.objects.create()
                conversation.participants.add(request.user, recipient)
                
                # Create first message
                Message.objects.create(
                    conversation=conversation,
                    sender=request.user,
                    content=message_content
                )
                
                messages.success(request, _('Conversation started successfully!'))
                return redirect('messaging:conversation', conversation_id=conversation.id)
    else:
        # Pre-fill recipient if provided in URL
        recipient_username = request.GET.get('to')
        initial_data = {}
        if recipient_username:
            initial_data['recipient'] = recipient_username
        
        form = NewConversationForm(user=request.user, initial=initial_data)
    
    context = {
        'form': form,
        'title': _('New Message')
    }
    
    return render(request, 'messaging/new_conversation.html', context)


@login_required
@require_POST
def send_message(request, conversation_id):
    """Send a message via AJAX"""
    conversation = get_object_or_404(
        Conversation,
        id=conversation_id,
        participants=request.user
    )
    
    content = request.POST.get('content', '').strip()
    if not content:
        return JsonResponse({'error': _('Message cannot be empty.')}, status=400)
    
    # Check if other participant has blocked current user
    other_participant = conversation.get_other_participant(request.user)
    if other_participant and Block.objects.filter(blocker=other_participant, blocked=request.user).exists():
        return JsonResponse({'error': _('You cannot send messages to this user.')}, status=400)
    
    message = Message.objects.create(
        conversation=conversation,
        sender=request.user,
        content=content
    )
    
    return JsonResponse({
        'success': True,
        'message': {
            'id': message.id,
            'content': message.content,
            'sender': message.sender.get_full_name(),
            'created_at': message.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
    })


@login_required
def delete_conversation(request, conversation_id):
    """Delete a conversation"""
    conversation = get_object_or_404(
        Conversation,
        id=conversation_id,
        participants=request.user
    )
    
    if request.method == 'POST':
        conversation.delete()
        messages.success(request, _('Conversation deleted successfully!'))
        return redirect('messaging:inbox')
    
    context = {
        'conversation': conversation,
        'other_participant': conversation.get_other_participant(request.user),
        'title': _('Delete Conversation')
    }
    
    return render(request, 'messaging/delete_conversation.html', context)
