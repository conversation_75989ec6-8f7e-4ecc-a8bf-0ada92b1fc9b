/* Athtar - Yemeni Social Media Platform Styles */

/* Yemeni Color Palette */
:root {
    --yemeni-red: #CE1126;
    --yemeni-white: #FFFFFF;
    --yemeni-black: #000000;
    --yemeni-gold: #FFD700;
    --yemeni-green: #007A3D;
    --yemeni-blue: #0066CC;
    --yemeni-sand: #F4E4BC;
    --yemeni-brown: #8B4513;
    --yemeni-gray: #6C757D;
}

/* Arabic Font */
body {
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
    background-color: #f8f9fa;
}

/* Custom Bootstrap Colors */
.bg-yemeni-primary {
    background-color: var(--yemeni-red) !important;
}

.bg-yemeni-secondary {
    background-color: var(--yemeni-green) !important;
}

.bg-yemeni-accent {
    background-color: var(--yemeni-gold) !important;
}

.text-yemeni-primary {
    color: var(--yemeni-red) !important;
}

.text-yemeni-secondary {
    color: var(--yemeni-green) !important;
}

.text-yemeni-accent {
    color: var(--yemeni-gold) !important;
}

.btn-yemeni-primary {
    background-color: var(--yemeni-red);
    border-color: var(--yemeni-red);
    color: white;
}

.btn-yemeni-primary:hover {
    background-color: #a00e1f;
    border-color: #a00e1f;
    color: white;
}

.btn-outline-yemeni-primary {
    color: var(--yemeni-red);
    border-color: var(--yemeni-red);
}

.btn-outline-yemeni-primary:hover {
    background-color: var(--yemeni-red);
    border-color: var(--yemeni-red);
    color: white;
}

/* Layout */
.main-content {
    margin-top: 76px;
    min-height: calc(100vh - 76px);
}

/* Navbar */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.search-form {
    width: 300px;
}

@media (max-width: 768px) {
    .search-form {
        width: 100%;
        margin: 1rem 0;
    }
}

/* Sidebar */
.sidebar {
    position: sticky;
    top: 90px;
    height: calc(100vh - 90px);
    overflow-y: auto;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--yemeni-gray);
    text-decoration: none;
    border-radius: 0.5rem;
    margin-bottom: 0.25rem;
    transition: all 0.2s;
}

.sidebar-link:hover {
    background-color: var(--yemeni-sand);
    color: var(--yemeni-red);
}

.sidebar-link.active {
    background-color: var(--yemeni-red);
    color: white;
}

.sidebar-link i {
    width: 20px;
    margin-left: 0.75rem;
}

.stat-item {
    padding: 0.5rem 0;
}

/* Right Sidebar */
.right-sidebar {
    position: sticky;
    top: 90px;
    height: calc(100vh - 90px);
    overflow-y: auto;
}

.trending-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.trending-item:last-child {
    border-bottom: none;
}

.suggested-user-item {
    padding: 0.5rem 0;
}

.culture-item {
    padding: 0.25rem 0;
}

.weather-info {
    padding: 1rem 0;
}

/* Posts */
.post-card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    border-radius: 0.75rem;
}

.post-header {
    padding: 1rem 1rem 0.5rem;
}

.post-content {
    padding: 0 1rem;
    line-height: 1.6;
}

.post-media {
    margin: 0.5rem 0;
}

.post-media img,
.post-media video {
    width: 100%;
    border-radius: 0.5rem;
}

.post-actions {
    padding: 0.5rem 1rem 1rem;
    border-top: 1px solid #eee;
    margin-top: 0.5rem;
}

.post-action-btn {
    background: none;
    border: none;
    color: var(--yemeni-gray);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s;
    margin-left: 0.5rem;
}

.post-action-btn:hover {
    background-color: var(--yemeni-sand);
    color: var(--yemeni-red);
}

.post-action-btn.liked {
    color: var(--yemeni-red);
}

/* Comments */
.comment-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}

.comment-item:last-child {
    border-bottom: none;
}

.comment-content {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.comment-reply {
    margin-right: 2rem;
    margin-top: 0.5rem;
}

/* Messages */
.conversation-item {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.conversation-item:hover {
    background-color: var(--yemeni-sand);
}

.conversation-item.unread {
    background-color: #fff3cd;
}

.message-bubble {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    margin-bottom: 0.5rem;
}

.message-bubble.sent {
    background-color: var(--yemeni-red);
    color: white;
    margin-right: auto;
    margin-left: 30%;
}

.message-bubble.received {
    background-color: #e9ecef;
    color: #333;
    margin-left: auto;
    margin-right: 30%;
}

/* Profile */
.profile-header {
    background: linear-gradient(135deg, var(--yemeni-red), var(--yemeni-green));
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border: 4px solid white;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.profile-stats {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: -1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Forms */
.form-control:focus {
    border-color: var(--yemeni-red);
    box-shadow: 0 0 0 0.2rem rgba(206, 17, 38, 0.25);
}

.form-select:focus {
    border-color: var(--yemeni-red);
    box-shadow: 0 0 0 0.2rem rgba(206, 17, 38, 0.25);
}

/* Messages */
.messages-container {
    margin-bottom: 1rem;
}

.alert {
    border-radius: 0.75rem;
    border: none;
}

/* Footer */
.footer {
    margin-top: auto;
}

.social-links a {
    font-size: 1.2rem;
    transition: color 0.2s;
}

.social-links a:hover {
    color: var(--yemeni-gold) !important;
}

.yemeni-symbols .badge {
    font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 992px) {
    .main-content {
        margin-top: 70px;
    }
    
    .sidebar,
    .right-sidebar {
        position: static;
        height: auto;
    }
}

/* RTL Adjustments */
.dropdown-menu-end {
    left: auto !important;
    right: 0 !important;
}

/* Loading Spinner */
.spinner-yemeni {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--yemeni-red);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hashtags */
.hashtag {
    color: var(--yemeni-blue);
    text-decoration: none;
    font-weight: 500;
}

.hashtag:hover {
    color: var(--yemeni-red);
    text-decoration: underline;
}

/* User mentions */
.mention {
    color: var(--yemeni-green);
    text-decoration: none;
    font-weight: 500;
}

.mention:hover {
    color: var(--yemeni-red);
    text-decoration: underline;
}
