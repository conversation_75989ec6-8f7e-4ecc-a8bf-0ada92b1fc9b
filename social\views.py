from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator

from .models import Follow, FollowRequest, Block

User = get_user_model()


@login_required
@require_POST
def toggle_follow(request, username):
    """Toggle follow/unfollow for a user"""
    user_to_follow = get_object_or_404(User, username=username)
    
    if user_to_follow == request.user:
        return JsonResponse({'error': _('You cannot follow yourself.')}, status=400)
    
    # Check if user is blocked
    if Block.objects.filter(blocker=user_to_follow, blocked=request.user).exists():
        return JsonResponse({'error': _('You cannot follow this user.')}, status=400)
    
    follow_obj = Follow.objects.filter(follower=request.user, following=user_to_follow).first()
    
    if follow_obj:
        # Unfollow
        follow_obj.delete()
        is_following = False
        action = 'unfollowed'
        messages.success(request, _('You unfollowed {}.').format(user_to_follow.get_full_name()))
    else:
        # Check if account is private
        if user_to_follow.profile.is_private:
            # Send follow request
            follow_request, created = FollowRequest.objects.get_or_create(
                from_user=request.user,
                to_user=user_to_follow,
                defaults={'status': 'pending'}
            )
            if created:
                messages.info(request, _('Follow request sent to {}.').format(user_to_follow.get_full_name()))
                action = 'requested'
            else:
                messages.info(request, _('Follow request already sent.'))
                action = 'already_requested'
            is_following = False
        else:
            # Follow directly
            Follow.objects.create(follower=request.user, following=user_to_follow)
            is_following = True
            action = 'followed'
            messages.success(request, _('You are now following {}.').format(user_to_follow.get_full_name()))
    
    return JsonResponse({
        'is_following': is_following,
        'action': action,
        'followers_count': user_to_follow.profile.followers_count
    })


@login_required
def follow_requests(request):
    """View pending follow requests"""
    requests = FollowRequest.objects.filter(
        to_user=request.user,
        status='pending'
    ).select_related('from_user', 'from_user__profile').order_by('-created_at')
    
    # Pagination
    paginator = Paginator(requests, 20)
    page_number = request.GET.get('page')
    requests = paginator.get_page(page_number)
    
    context = {
        'requests': requests,
        'title': _('Follow Requests')
    }
    
    return render(request, 'social/follow_requests.html', context)


@login_required
@require_POST
def handle_follow_request(request, request_id):
    """Accept or reject a follow request"""
    follow_request = get_object_or_404(
        FollowRequest,
        id=request_id,
        to_user=request.user,
        status='pending'
    )
    
    action = request.POST.get('action')
    
    if action == 'accept':
        follow_request.status = 'accepted'
        follow_request.save()
        messages.success(
            request,
            _('You accepted the follow request from {}.').format(follow_request.from_user.get_full_name())
        )
    elif action == 'reject':
        follow_request.status = 'rejected'
        follow_request.save()
        messages.info(
            request,
            _('You rejected the follow request from {}.').format(follow_request.from_user.get_full_name())
        )
    
    return redirect('social:follow_requests')


@login_required
@require_POST
def block_user(request, username):
    """Block a user"""
    user_to_block = get_object_or_404(User, username=username)
    
    if user_to_block == request.user:
        return JsonResponse({'error': _('You cannot block yourself.')}, status=400)
    
    block_obj, created = Block.objects.get_or_create(
        blocker=request.user,
        blocked=user_to_block
    )
    
    if created:
        messages.success(request, _('You blocked {}.').format(user_to_block.get_full_name()))
    else:
        messages.info(request, _('User is already blocked.'))
    
    return JsonResponse({'blocked': True})


@login_required
@require_POST
def unblock_user(request, username):
    """Unblock a user"""
    user_to_unblock = get_object_or_404(User, username=username)
    
    block_obj = Block.objects.filter(
        blocker=request.user,
        blocked=user_to_unblock
    ).first()
    
    if block_obj:
        block_obj.delete()
        messages.success(request, _('You unblocked {}.').format(user_to_unblock.get_full_name()))
    
    return JsonResponse({'blocked': False})


@login_required
def blocked_users(request):
    """View blocked users"""
    blocked = Block.objects.filter(
        blocker=request.user
    ).select_related('blocked', 'blocked__profile').order_by('-created_at')
    
    # Pagination
    paginator = Paginator(blocked, 20)
    page_number = request.GET.get('page')
    blocked = paginator.get_page(page_number)
    
    context = {
        'blocked_users': blocked,
        'title': _('Blocked Users')
    }
    
    return render(request, 'social/blocked_users.html', context)


@login_required
def suggested_users(request):
    """View suggested users to follow"""
    # Get users that current user is not following and not blocked
    following_ids = Follow.objects.filter(follower=request.user).values_list('following_id', flat=True)
    blocked_ids = Block.objects.filter(blocker=request.user).values_list('blocked_id', flat=True)
    
    suggested = User.objects.exclude(
        id__in=list(following_ids) + list(blocked_ids) + [request.user.id]
    ).select_related('profile').order_by('-profile__followers_count')[:20]
    
    context = {
        'suggested_users': suggested,
        'title': _('Suggested Users')
    }
    
    return render(request, 'social/suggested_users.html', context)
