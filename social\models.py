from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class Follow(models.Model):
    """Follow relationship between users"""
    
    follower = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='following',
        verbose_name=_('follower')
    )
    following = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='followers',
        verbose_name=_('following')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Follow')
        verbose_name_plural = _('Follows')
        unique_together = ('follower', 'following')
        indexes = [
            models.Index(fields=['follower', '-created_at']),
            models.Index(fields=['following', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.follower.username} follows {self.following.username}"
    
    def clean(self):
        from django.core.exceptions import ValidationError
        if self.follower == self.following:
            raise ValidationError(_('Users cannot follow themselves.'))


class FollowRequest(models.Model):
    """Follow request for private accounts"""
    
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('accepted', _('Accepted')),
        ('rejected', _('Rejected')),
    ]
    
    from_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_follow_requests',
        verbose_name=_('from user')
    )
    to_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='received_follow_requests',
        verbose_name=_('to user')
    )
    status = models.CharField(
        _('status'),
        max_length=10,
        choices=STATUS_CHOICES,
        default='pending'
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Follow Request')
        verbose_name_plural = _('Follow Requests')
        unique_together = ('from_user', 'to_user')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.from_user.username} → {self.to_user.username} ({self.status})"
    
    def clean(self):
        from django.core.exceptions import ValidationError
        if self.from_user == self.to_user:
            raise ValidationError(_('Users cannot send follow requests to themselves.'))


class Block(models.Model):
    """Block relationship between users"""
    
    blocker = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='blocking',
        verbose_name=_('blocker')
    )
    blocked = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='blocked_by',
        verbose_name=_('blocked')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Block')
        verbose_name_plural = _('Blocks')
        unique_together = ('blocker', 'blocked')
    
    def __str__(self):
        return f"{self.blocker.username} blocked {self.blocked.username}"
    
    def clean(self):
        from django.core.exceptions import ValidationError
        if self.blocker == self.blocked:
            raise ValidationError(_('Users cannot block themselves.'))


# Signals to update follower/following counts
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

@receiver(post_save, sender=Follow)
def increment_follow_counts(sender, instance, created, **kwargs):
    if created:
        # Update follower's following count
        follower_profile = instance.follower.profile
        follower_profile.following_count += 1
        follower_profile.save(update_fields=['following_count'])
        
        # Update following's followers count
        following_profile = instance.following.profile
        following_profile.followers_count += 1
        following_profile.save(update_fields=['followers_count'])

@receiver(post_delete, sender=Follow)
def decrement_follow_counts(sender, instance, **kwargs):
    # Update follower's following count
    follower_profile = instance.follower.profile
    follower_profile.following_count -= 1
    follower_profile.save(update_fields=['following_count'])
    
    # Update following's followers count
    following_profile = instance.following.profile
    following_profile.followers_count -= 1
    following_profile.save(update_fields=['followers_count'])

@receiver(post_save, sender=FollowRequest)
def handle_follow_request_accepted(sender, instance, **kwargs):
    """Create Follow relationship when request is accepted"""
    if instance.status == 'accepted':
        Follow.objects.get_or_create(
            follower=instance.from_user,
            following=instance.to_user
        )

@receiver(post_save, sender=Block)
def handle_block_created(sender, instance, created, **kwargs):
    """Remove follow relationships when blocking"""
    if created:
        # Remove existing follow relationships
        Follow.objects.filter(
            follower=instance.blocker,
            following=instance.blocked
        ).delete()
        
        Follow.objects.filter(
            follower=instance.blocked,
            following=instance.blocker
        ).delete()
        
        # Remove pending follow requests
        FollowRequest.objects.filter(
            from_user=instance.blocker,
            to_user=instance.blocked
        ).delete()
        
        FollowRequest.objects.filter(
            from_user=instance.blocked,
            to_user=instance.blocker
        ).delete()
