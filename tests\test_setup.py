"""
Basic tests to verify Athtar platform setup
"""

import os
import sys
from pathlib import Path
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.conf import settings

User = get_user_model()


class SetupTestCase(TestCase):
    """Test basic setup and configuration"""
    
    def test_django_settings(self):
        """Test Django settings are properly configured"""
        self.assertTrue(hasattr(settings, 'SECRET_KEY'))
        self.assertTrue(hasattr(settings, 'INSTALLED_APPS'))
        self.assertIn('accounts', settings.INSTALLED_APPS)
        self.assertIn('profiles', settings.INSTALLED_APPS)
        self.assertIn('posts', settings.INSTALLED_APPS)
        self.assertIn('social', settings.INSTALLED_APPS)
        self.assertIn('messaging', settings.INSTALLED_APPS)
        self.assertIn('search', settings.INSTALLED_APPS)
    
    def test_custom_user_model(self):
        """Test custom user model is properly configured"""
        self.assertEqual(settings.AUTH_USER_MODEL, 'accounts.User')
        
        # Test user creation
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.username, 'testuser')
        self.assertTrue(user.check_password('testpass123'))
    
    def test_arabic_language_support(self):
        """Test Arabic language configuration"""
        self.assertEqual(settings.LANGUAGE_CODE, 'ar')
        self.assertEqual(settings.TIME_ZONE, 'Asia/Aden')
        self.assertTrue(settings.USE_I18N)
        self.assertTrue(settings.USE_TZ)
    
    def test_media_configuration(self):
        """Test media files configuration"""
        self.assertTrue(hasattr(settings, 'MEDIA_URL'))
        self.assertTrue(hasattr(settings, 'MEDIA_ROOT'))
        self.assertEqual(settings.MEDIA_URL, '/media/')
    
    def test_static_configuration(self):
        """Test static files configuration"""
        self.assertTrue(hasattr(settings, 'STATIC_URL'))
        self.assertTrue(hasattr(settings, 'STATIC_ROOT'))
        self.assertEqual(settings.STATIC_URL, '/static/')


class URLTestCase(TestCase):
    """Test URL configuration"""
    
    def test_main_urls(self):
        """Test main URL patterns are accessible"""
        # Test redirect from root
        response = self.client.get('/')
        self.assertEqual(response.status_code, 302)  # Redirect to feed
        
        # Test login page
        response = self.client.get('/accounts/login/')
        self.assertEqual(response.status_code, 200)
        
        # Test register page
        response = self.client.get('/accounts/register/')
        self.assertEqual(response.status_code, 200)


class UserRegistrationTestCase(TestCase):
    """Test user registration functionality"""
    
    def test_user_registration(self):
        """Test user can register successfully"""
        response = self.client.post('/accounts/register/', {
            'email': '<EMAIL>',
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'password1': 'complexpass123',
            'password2': 'complexpass123'
        })
        
        # Should redirect after successful registration
        self.assertEqual(response.status_code, 302)
        
        # User should be created
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
        
        # Profile should be created automatically
        user = User.objects.get(email='<EMAIL>')
        self.assertTrue(hasattr(user, 'profile'))


class ProfileTestCase(TestCase):
    """Test profile functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='profileuser',
            first_name='Profile',
            last_name='User',
            password='testpass123'
        )
    
    def test_profile_creation(self):
        """Test profile is created automatically"""
        self.assertTrue(hasattr(self.user, 'profile'))
        self.assertEqual(self.user.profile.user, self.user)
    
    def test_profile_view(self):
        """Test profile view is accessible"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(f'/profile/{self.user.username}/')
        self.assertEqual(response.status_code, 200)


class PostTestCase(TestCase):
    """Test post functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='poster',
            first_name='Post',
            last_name='User',
            password='testpass123'
        )
        self.client.login(email='<EMAIL>', password='testpass123')
    
    def test_feed_access(self):
        """Test feed is accessible for authenticated users"""
        response = self.client.get('/feed/')
        self.assertEqual(response.status_code, 200)
    
    def test_post_creation_page(self):
        """Test post creation page is accessible"""
        response = self.client.get('/create/')
        self.assertEqual(response.status_code, 200)


class SecurityTestCase(TestCase):
    """Test security configurations"""
    
    def test_csrf_protection(self):
        """Test CSRF protection is enabled"""
        self.assertIn('django.middleware.csrf.CsrfViewMiddleware', settings.MIDDLEWARE)
    
    def test_xss_protection(self):
        """Test XSS protection is enabled"""
        self.assertTrue(settings.SECURE_BROWSER_XSS_FILTER)
        self.assertTrue(settings.SECURE_CONTENT_TYPE_NOSNIFF)
    
    def test_clickjacking_protection(self):
        """Test clickjacking protection"""
        self.assertEqual(settings.X_FRAME_OPTIONS, 'DENY')


def run_tests():
    """Run all tests and return results"""
    import django
    from django.test.utils import get_runner
    from django.conf import settings
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'athtar.settings')
    django.setup()
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['tests'])
    
    if failures:
        print(f"❌ {failures} test(s) failed")
        return False
    else:
        print("✅ All tests passed!")
        return True


if __name__ == '__main__':
    run_tests()
