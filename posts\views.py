from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth import get_user_model

from .models import Post, Comment, Like, CommentLike, Share
from .forms import PostCreateForm, PostUpdateForm, CommentForm
from social.models import Follow

User = get_user_model()


@login_required
def feed(request):
    """Main feed view showing posts from followed users"""
    # Get posts from followed users and own posts
    following_users = Follow.objects.filter(follower=request.user).values_list('following', flat=True)
    posts = Post.objects.filter(
        author__in=list(following_users) + [request.user.id]
    ).select_related('author', 'author__profile').prefetch_related('likes', 'comments')
    
    # If user doesn't follow anyone, show recent posts from all users
    if not posts.exists():
        posts = Post.objects.all().select_related('author', 'author__profile').prefetch_related('likes', 'comments')
    
    # Pagination
    paginator = Paginator(posts, 10)
    page_number = request.GET.get('page')
    posts = paginator.get_page(page_number)
    
    # Post creation form
    form = PostCreateForm()
    
    context = {
        'posts': posts,
        'form': form,
        'title': _('Feed')
    }
    
    return render(request, 'posts/feed.html', context)


class PostCreateView(LoginRequiredMixin, CreateView):
    """View for creating new posts"""
    model = Post
    form_class = PostCreateForm
    template_name = 'posts/post_create.html'
    success_url = reverse_lazy('posts:feed')
    
    def form_valid(self, form):
        form.instance.author = self.request.user
        messages.success(self.request, _('Post created successfully!'))
        return super().form_valid(form)


class PostDetailView(DetailView):
    """View for displaying post details"""
    model = Post
    template_name = 'posts/post_detail.html'
    context_object_name = 'post'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['comment_form'] = CommentForm()
        context['comments'] = self.object.get_comments()
        return context


class PostUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """View for updating posts"""
    model = Post
    form_class = PostUpdateForm
    template_name = 'posts/post_update.html'
    
    def test_func(self):
        post = self.get_object()
        return self.request.user == post.author
    
    def form_valid(self, form):
        messages.success(self.request, _('Post updated successfully!'))
        return super().form_valid(form)


class PostDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """View for deleting posts"""
    model = Post
    template_name = 'posts/post_delete.html'
    success_url = reverse_lazy('posts:feed')
    
    def test_func(self):
        post = self.get_object()
        return self.request.user == post.author
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Post deleted successfully!'))
        return super().delete(request, *args, **kwargs)


@login_required
@require_POST
def toggle_like(request, post_id):
    """Toggle like on a post"""
    post = get_object_or_404(Post, id=post_id)
    like, created = Like.objects.get_or_create(user=request.user, post=post)
    
    if not created:
        like.delete()
        liked = False
    else:
        liked = True
    
    return JsonResponse({
        'liked': liked,
        'likes_count': post.likes_count
    })


@login_required
@require_POST
def toggle_comment_like(request, comment_id):
    """Toggle like on a comment"""
    comment = get_object_or_404(Comment, id=comment_id)
    like, created = CommentLike.objects.get_or_create(user=request.user, comment=comment)
    
    if not created:
        like.delete()
        liked = False
    else:
        liked = True
    
    return JsonResponse({
        'liked': liked,
        'likes_count': comment.likes_count
    })


@login_required
@require_POST
def add_comment(request, post_id):
    """Add comment to a post"""
    post = get_object_or_404(Post, id=post_id)
    form = CommentForm(request.POST)
    
    if form.is_valid():
        comment = form.save(commit=False)
        comment.author = request.user
        comment.post = post
        
        # Check if it's a reply
        parent_id = request.POST.get('parent_id')
        if parent_id:
            parent_comment = get_object_or_404(Comment, id=parent_id)
            comment.parent = parent_comment
        
        comment.save()
        messages.success(request, _('Comment added successfully!'))
    else:
        messages.error(request, _('Error adding comment.'))
    
    return redirect('posts:detail', pk=post_id)


@login_required
@require_POST
def share_post(request, post_id):
    """Share a post"""
    post = get_object_or_404(Post, id=post_id)
    share, created = Share.objects.get_or_create(user=request.user, post=post)
    
    if created:
        messages.success(request, _('Post shared successfully!'))
    else:
        messages.info(request, _('You have already shared this post.'))
    
    return JsonResponse({
        'shared': created,
        'shares_count': post.shares_count
    })


@login_required
def explore(request):
    """Explore page showing trending posts"""
    posts = Post.objects.all().order_by('-likes_count', '-created_at')
    
    # Pagination
    paginator = Paginator(posts, 15)
    page_number = request.GET.get('page')
    posts = paginator.get_page(page_number)
    
    context = {
        'posts': posts,
        'title': _('Explore')
    }
    
    return render(request, 'posts/explore.html', context)
