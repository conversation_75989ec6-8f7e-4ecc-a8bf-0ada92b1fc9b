from django.shortcuts import render
from django.contrib.auth import get_user_model
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required

from .forms import SearchForm, AdvancedSearchForm
from posts.models import Post
from social.models import Block

User = get_user_model()


def search(request):
    """Main search view"""
    form = SearchForm(request.GET or None)
    users = []
    posts = []
    query = None
    search_type = 'all'
    
    if form.is_valid():
        query = form.cleaned_data['query']
        search_type = form.cleaned_data.get('search_type', 'all')
        
        # Get blocked users to exclude from results
        blocked_users = []
        if request.user.is_authenticated:
            blocked_users = Block.objects.filter(
                Q(blocker=request.user) | Q(blocked=request.user)
            ).values_list('blocker_id', 'blocked_id')
            blocked_user_ids = set()
            for blocker_id, blocked_id in blocked_users:
                blocked_user_ids.add(blocker_id)
                blocked_user_ids.add(blocked_id)
            blocked_user_ids.discard(request.user.id)
        else:
            blocked_user_ids = set()
        
        # Search users
        if search_type in ['all', 'users']:
            users = User.objects.filter(
                Q(username__icontains=query) |
                Q(first_name__icontains=query) |
                Q(last_name__icontains=query) |
                Q(profile__location__icontains=query)
            ).exclude(
                id__in=blocked_user_ids
            ).select_related('profile').distinct()
        
        # Search posts
        if search_type in ['all', 'posts']:
            posts_queryset = Post.objects.filter(
                content__icontains=query
            ).exclude(
                author_id__in=blocked_user_ids
            ).select_related('author', 'author__profile')
            
            # Filter private accounts if user is not authenticated
            if not request.user.is_authenticated:
                posts_queryset = posts_queryset.exclude(author__profile__is_private=True)
            elif request.user.is_authenticated:
                # Exclude posts from private accounts that user doesn't follow
                from social.models import Follow
                following_ids = Follow.objects.filter(follower=request.user).values_list('following_id', flat=True)
                posts_queryset = posts_queryset.exclude(
                    Q(author__profile__is_private=True) & 
                    ~Q(author_id__in=following_ids) & 
                    ~Q(author=request.user)
                )
            
            posts = posts_queryset.distinct()
    
    # Pagination
    users_paginator = Paginator(users, 10)
    posts_paginator = Paginator(posts, 10)
    
    users_page = users_paginator.get_page(request.GET.get('users_page'))
    posts_page = posts_paginator.get_page(request.GET.get('posts_page'))
    
    context = {
        'form': form,
        'users': users_page,
        'posts': posts_page,
        'query': query,
        'search_type': search_type,
        'title': _('Search Results') if query else _('Search')
    }
    
    return render(request, 'search/search.html', context)


@login_required
def advanced_search(request):
    """Advanced search with filters"""
    form = AdvancedSearchForm(request.GET or None)
    users = []
    posts = []
    query = None
    
    if form.is_valid():
        query = form.cleaned_data['query']
        location = form.cleaned_data.get('location')
        has_image = form.cleaned_data.get('has_image')
        has_video = form.cleaned_data.get('has_video')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')
        
        # Get blocked users
        blocked_users = Block.objects.filter(
            Q(blocker=request.user) | Q(blocked=request.user)
        ).values_list('blocker_id', 'blocked_id')
        blocked_user_ids = set()
        for blocker_id, blocked_id in blocked_users:
            blocked_user_ids.add(blocker_id)
            blocked_user_ids.add(blocked_id)
        blocked_user_ids.discard(request.user.id)
        
        # Search users with filters
        users_query = Q(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query)
        )
        
        if location:
            users_query &= Q(profile__location__icontains=location)
        
        users = User.objects.filter(users_query).exclude(
            id__in=blocked_user_ids
        ).select_related('profile').distinct()
        
        # Search posts with filters
        posts_query = Q(content__icontains=query)
        
        if has_image:
            posts_query &= Q(image__isnull=False)
        
        if has_video:
            posts_query &= Q(video__isnull=False)
        
        if date_from:
            posts_query &= Q(created_at__date__gte=date_from)
        
        if date_to:
            posts_query &= Q(created_at__date__lte=date_to)
        
        posts_queryset = Post.objects.filter(posts_query).exclude(
            author_id__in=blocked_user_ids
        ).select_related('author', 'author__profile')
        
        # Filter private accounts
        from social.models import Follow
        following_ids = Follow.objects.filter(follower=request.user).values_list('following_id', flat=True)
        posts_queryset = posts_queryset.exclude(
            Q(author__profile__is_private=True) & 
            ~Q(author_id__in=following_ids) & 
            ~Q(author=request.user)
        )
        
        posts = posts_queryset.distinct()
    
    # Pagination
    users_paginator = Paginator(users, 10)
    posts_paginator = Paginator(posts, 10)
    
    users_page = users_paginator.get_page(request.GET.get('users_page'))
    posts_page = posts_paginator.get_page(request.GET.get('posts_page'))
    
    context = {
        'form': form,
        'users': users_page,
        'posts': posts_page,
        'query': query,
        'title': _('Advanced Search')
    }
    
    return render(request, 'search/advanced_search.html', context)


def hashtag_search(request, hashtag):
    """Search posts by hashtag"""
    posts = Post.objects.filter(
        content__icontains=f'#{hashtag}'
    ).select_related('author', 'author__profile')
    
    # Filter blocked users and private accounts
    if request.user.is_authenticated:
        blocked_users = Block.objects.filter(
            Q(blocker=request.user) | Q(blocked=request.user)
        ).values_list('blocker_id', 'blocked_id')
        blocked_user_ids = set()
        for blocker_id, blocked_id in blocked_users:
            blocked_user_ids.add(blocker_id)
            blocked_user_ids.add(blocked_id)
        blocked_user_ids.discard(request.user.id)
        
        posts = posts.exclude(author_id__in=blocked_user_ids)
        
        # Filter private accounts
        from social.models import Follow
        following_ids = Follow.objects.filter(follower=request.user).values_list('following_id', flat=True)
        posts = posts.exclude(
            Q(author__profile__is_private=True) & 
            ~Q(author_id__in=following_ids) & 
            ~Q(author=request.user)
        )
    else:
        posts = posts.exclude(author__profile__is_private=True)
    
    posts = posts.distinct().order_by('-created_at')
    
    # Pagination
    paginator = Paginator(posts, 15)
    page_number = request.GET.get('page')
    posts = paginator.get_page(page_number)
    
    context = {
        'posts': posts,
        'hashtag': hashtag,
        'title': f'#{hashtag}'
    }
    
    return render(request, 'search/hashtag.html', context)
