{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}عثتر - Athtar{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{% if user.is_authenticated %}authenticated{% else %}anonymous{% endif %}">
    
    <!-- Navigation -->
    {% include 'includes/navbar.html' %}
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <div class="row">
                
                <!-- Sidebar -->
                {% if user.is_authenticated %}
                <div class="col-lg-3 d-none d-lg-block">
                    {% include 'includes/sidebar.html' %}
                </div>
                {% endif %}
                
                <!-- Content Area -->
                <div class="col-lg-{% if user.is_authenticated %}6{% else %}12{% endif %} col-md-12">
                    
                    <!-- Messages -->
                    {% if messages %}
                        <div class="messages-container">
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <!-- Page Content -->
                    {% block content %}{% endblock %}
                </div>
                
                <!-- Right Sidebar -->
                {% if user.is_authenticated %}
                <div class="col-lg-3 d-none d-lg-block">
                    {% include 'includes/right_sidebar.html' %}
                </div>
                {% endif %}
                
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    {% include 'includes/footer.html' %}
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
