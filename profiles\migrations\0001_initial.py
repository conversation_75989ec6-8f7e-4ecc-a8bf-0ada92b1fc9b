# Generated by Django 4.2.22 on 2025-07-06 19:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import profiles.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bio', models.TextField(blank=True, max_length=500, verbose_name='bio')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='location')),
                ('website', models.URLField(blank=True, verbose_name='website')),
                ('profile_image', models.ImageField(blank=True, default='profiles/default.jpg', upload_to=profiles.models.user_profile_image_path, verbose_name='profile image')),
                ('cover_image', models.ImageField(blank=True, null=True, upload_to=profiles.models.user_cover_image_path, verbose_name='cover image')),
                ('is_private', models.BooleanField(default=False, verbose_name='private account')),
                ('followers_count', models.PositiveIntegerField(default=0, verbose_name='followers count')),
                ('following_count', models.PositiveIntegerField(default=0, verbose_name='following count')),
                ('posts_count', models.PositiveIntegerField(default=0, verbose_name='posts count')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Profile',
                'verbose_name_plural': 'Profiles',
                'ordering': ['-created_at'],
            },
        ),
    ]
