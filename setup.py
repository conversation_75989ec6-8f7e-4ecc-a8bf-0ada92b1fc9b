#!/usr/bin/env python3
"""
Athtar Social Media Platform Setup Script
This script helps set up the Athtar platform for development.
"""

import os
import sys
import subprocess
import secrets
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}: {e}")
        print(f"Output: {e.output}")
        return False

def create_env_file():
    """Create .env file with default settings."""
    env_content = f"""# Athtar Environment Configuration
SECRET_KEY={secrets.token_urlsafe(50)}
DEBUG=True
DATABASE_URL=sqlite:///db.sqlite3
REDIS_URL=redis://localhost:6379/0

# Email Configuration (Optional)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# Media and Static Files
MEDIA_ROOT=media
STATIC_ROOT=staticfiles
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    print("✅ Created .env file with default settings")

def create_directories():
    """Create necessary directories."""
    directories = [
        'media/profiles',
        'media/posts',
        'static/images',
        'staticfiles',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    print("✅ Created necessary directories")

def main():
    """Main setup function."""
    print("🇾🇪 Welcome to Athtar Social Media Platform Setup!")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        create_env_file()
    else:
        print("ℹ️  .env file already exists, skipping creation")
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        print("❌ Failed to install dependencies. Please check your pip installation.")
        sys.exit(1)
    
    # Run migrations
    if not run_command("python manage.py makemigrations", "Creating database migrations"):
        print("❌ Failed to create migrations")
        sys.exit(1)
    
    if not run_command("python manage.py migrate", "Applying database migrations"):
        print("❌ Failed to apply migrations")
        sys.exit(1)
    
    # Collect static files
    if not run_command("python manage.py collectstatic --noinput", "Collecting static files"):
        print("⚠️  Warning: Failed to collect static files")
    
    # Create superuser prompt
    print("\n" + "=" * 50)
    print("🔐 Create Admin User")
    print("=" * 50)
    
    create_superuser = input("Would you like to create an admin user? (y/n): ").lower().strip()
    if create_superuser in ['y', 'yes']:
        print("Please follow the prompts to create an admin user:")
        os.system("python manage.py createsuperuser")
    
    # Final instructions
    print("\n" + "=" * 50)
    print("🎉 Setup Complete!")
    print("=" * 50)
    print("Your Athtar social media platform is ready!")
    print("\nNext steps:")
    print("1. Start the development server:")
    print("   python manage.py runserver")
    print("\n2. Open your browser and go to:")
    print("   http://127.0.0.1:8000")
    print("\n3. Admin panel (if you created an admin user):")
    print("   http://127.0.0.1:8000/admin")
    print("\n4. For real-time messaging, make sure Redis is running:")
    print("   redis-server")
    print("\n🇾🇪 Welcome to Athtar - Connecting Yemen to the World!")
    print("=" * 50)

if __name__ == "__main__":
    main()
