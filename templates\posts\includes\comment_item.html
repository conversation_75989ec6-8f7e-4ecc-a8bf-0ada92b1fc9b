{% load static %}
{% load i18n %}
{% load humanize %}

<div class="comment-item">
    <div class="d-flex">
        <a href="{% url 'profiles:profile' username=comment.author.username %}">
            <img src="{{ comment.author.profile.profile_image.url }}" alt="{{ comment.author.get_full_name }}" 
                 class="rounded-circle me-2" width="32" height="32">
        </a>
        <div class="flex-grow-1">
            <div class="comment-content">
                <div class="d-flex align-items-center mb-1">
                    <a href="{% url 'profiles:profile' username=comment.author.username %}" 
                       class="text-decoration-none text-dark">
                        <strong class="small">{{ comment.author.get_full_name }}</strong>
                    </a>
                    <span class="text-muted small ms-2">{{ comment.created_at|naturaltime }}</span>
                </div>
                <p class="mb-1 small">{{ comment.content|linebreaks }}</p>
            </div>
            
            <div class="comment-actions">
                <button class="btn btn-link btn-sm p-0 me-3 comment-like-btn {% if comment.is_liked_by:user %}liked{% endif %}" 
                        data-comment-id="{{ comment.id }}">
                    <i class="{% if comment.is_liked_by:user %}fas{% else %}far{% endif %} fa-heart"></i>
                    <span class="like-count">{{ comment.likes_count }}</span>
                </button>
                
                <button class="btn btn-link btn-sm p-0 me-3" onclick="toggleReplyForm({{ comment.id }})">
                    <i class="fas fa-reply"></i> {% trans 'Reply' %}
                </button>
                
                {% if user == comment.author %}
                    <button class="btn btn-link btn-sm p-0 text-danger">
                        <i class="fas fa-trash"></i> {% trans 'Delete' %}
                    </button>
                {% endif %}
            </div>
            
            <!-- Reply Form -->
            {% if user.is_authenticated %}
                <div class="reply-form mt-2" id="reply-form-{{ comment.id }}" style="display: none;">
                    <form method="post" action="{% url 'posts:add_comment' post_id=comment.post.id %}">
                        {% csrf_token %}
                        <input type="hidden" name="parent_id" value="{{ comment.id }}">
                        <div class="d-flex">
                            <img src="{{ user.profile.profile_image.url }}" alt="{{ user.get_full_name }}" 
                                 class="rounded-circle me-2" width="24" height="24">
                            <div class="flex-grow-1">
                                <textarea name="content" class="form-control form-control-sm" 
                                        placeholder="{% trans 'Write a reply...' %}" rows="1" required></textarea>
                                <div class="text-end mt-1">
                                    <button type="button" class="btn btn-link btn-sm" onclick="toggleReplyForm({{ comment.id }})">
                                        {% trans 'Cancel' %}
                                    </button>
                                    <button type="submit" class="btn btn-yemeni-primary btn-sm">
                                        {% trans 'Reply' %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            {% endif %}
            
            <!-- Replies -->
            {% if comment.get_replies %}
                <div class="comment-replies mt-2">
                    {% for reply in comment.get_replies %}
                        <div class="comment-reply">
                            <div class="d-flex">
                                <a href="{% url 'profiles:profile' username=reply.author.username %}">
                                    <img src="{{ reply.author.profile.profile_image.url }}" alt="{{ reply.author.get_full_name }}" 
                                         class="rounded-circle me-2" width="24" height="24">
                                </a>
                                <div class="flex-grow-1">
                                    <div class="comment-content">
                                        <div class="d-flex align-items-center mb-1">
                                            <a href="{% url 'profiles:profile' username=reply.author.username %}" 
                                               class="text-decoration-none text-dark">
                                                <strong class="small">{{ reply.author.get_full_name }}</strong>
                                            </a>
                                            <span class="text-muted small ms-2">{{ reply.created_at|naturaltime }}</span>
                                        </div>
                                        <p class="mb-1 small">{{ reply.content|linebreaks }}</p>
                                    </div>
                                    
                                    <div class="comment-actions">
                                        <button class="btn btn-link btn-sm p-0 me-3 comment-like-btn {% if reply.is_liked_by:user %}liked{% endif %}" 
                                                data-comment-id="{{ reply.id }}">
                                            <i class="{% if reply.is_liked_by:user %}fas{% else %}far{% endif %} fa-heart"></i>
                                            <span class="like-count">{{ reply.likes_count }}</span>
                                        </button>
                                        
                                        {% if user == reply.author %}
                                            <button class="btn btn-link btn-sm p-0 text-danger">
                                                <i class="fas fa-trash"></i> {% trans 'Delete' %}
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function toggleReplyForm(commentId) {
    const replyForm = document.getElementById('reply-form-' + commentId);
    if (replyForm.style.display === 'none') {
        replyForm.style.display = 'block';
        replyForm.querySelector('textarea').focus();
    } else {
        replyForm.style.display = 'none';
    }
}
</script>
