{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans 'Register' %} - عثتر{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        
        <!-- Logo and Welcome -->
        <div class="text-center mb-4">
            <img src="{% static 'images/logo.png' %}" alt="عثتر" height="80" class="mb-3">
            <h2 class="text-yemeni-primary">انضم إلى عثتر</h2>
            <p class="text-muted">{% trans 'Join the Yemeni social network' %}</p>
        </div>
        
        <!-- Registration Form -->
        <div class="card shadow">
            <div class="card-body p-4">
                <h4 class="card-title text-center mb-4">{% trans 'Create Account' %}</h4>
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                {{ form.first_name.label }}
                            </label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="text-danger small">
                                    {{ form.first_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                {{ form.last_name.label }}
                            </label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="text-danger small">
                                    {{ form.last_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">
                            {{ form.email.label }}
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-danger small">
                                {{ form.email.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            {{ form.username.label }}
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small">
                                {{ form.username.errors.0 }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            {% trans 'Username can only contain letters, numbers, and underscores.' %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">
                                {{ form.password1.label }}
                            </label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <div class="text-danger small">
                                    {{ form.password1.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">
                                {{ form.password2.label }}
                            </label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <div class="text-danger small">
                                    {{ form.password2.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                {% trans 'I agree to the' %}
                                <a href="#" class="text-yemeni-primary">{% trans 'Terms of Service' %}</a>
                                {% trans 'and' %}
                                <a href="#" class="text-yemeni-primary">{% trans 'Privacy Policy' %}</a>
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-yemeni-primary btn-lg">
                            {% trans 'Create Account' %}
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="mb-0">
                        {% trans "Already have an account?" %}
                        <a href="{% url 'accounts:login' %}" class="text-yemeni-primary">
                            {% trans 'Login here' %}
                        </a>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Cultural Elements -->
        <div class="text-center mt-4">
            <div class="yemeni-pattern">
                <span class="badge bg-yemeni-primary me-2">🏛️</span>
                <span class="text-muted">{% trans 'Connecting Yemen to the World' %}</span>
                <span class="badge bg-yemeni-primary ms-2">🌍</span>
            </div>
        </div>
        
    </div>
</div>
{% endblock %}
