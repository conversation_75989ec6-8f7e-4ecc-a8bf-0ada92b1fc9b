{% load static %}
{% load i18n %}

<div class="sidebar">
    <div class="card">
        <div class="card-body">
            
            <!-- User Profile Summary -->
            <div class="user-summary text-center mb-4">
                <img src="{{ user.profile.profile_image.url }}" alt="{{ user.get_full_name }}" 
                     class="rounded-circle mb-2" width="80" height="80">
                <h6 class="mb-1">{{ user.get_full_name }}</h6>
                <p class="text-muted small">@{{ user.username }}</p>
                
                <!-- Stats -->
                <div class="row text-center">
                    <div class="col-4">
                        <div class="stat-item">
                            <strong>{{ user.profile.posts_count }}</strong>
                            <small class="d-block text-muted">{% trans 'Posts' %}</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <strong>{{ user.profile.followers_count }}</strong>
                            <small class="d-block text-muted">{% trans 'Followers' %}</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <strong>{{ user.profile.following_count }}</strong>
                            <small class="d-block text-muted">{% trans 'Following' %}</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Navigation Menu -->
            <div class="sidebar-menu">
                <a href="{% url 'posts:feed' %}" class="sidebar-link {% if request.resolver_match.url_name == 'feed' %}active{% endif %}">
                    <i class="fas fa-home"></i>
                    <span>{% trans 'Home' %}</span>
                </a>
                
                <a href="{% url 'profiles:profile' username=user.username %}" class="sidebar-link">
                    <i class="fas fa-user"></i>
                    <span>{% trans 'Profile' %}</span>
                </a>
                
                <a href="{% url 'posts:explore' %}" class="sidebar-link {% if request.resolver_match.url_name == 'explore' %}active{% endif %}">
                    <i class="fas fa-compass"></i>
                    <span>{% trans 'Explore' %}</span>
                </a>
                
                <a href="{% url 'messaging:inbox' %}" class="sidebar-link {% if 'messaging' in request.resolver_match.namespace %}active{% endif %}">
                    <i class="fas fa-envelope"></i>
                    <span>{% trans 'Messages' %}</span>
                </a>
                
                <a href="{% url 'social:follow_requests' %}" class="sidebar-link">
                    <i class="fas fa-user-plus"></i>
                    <span>{% trans 'Follow Requests' %}</span>
                </a>
                
                <a href="{% url 'social:suggested_users' %}" class="sidebar-link">
                    <i class="fas fa-users"></i>
                    <span>{% trans 'Suggested Users' %}</span>
                </a>
                
                <a href="{% url 'search:advanced_search' %}" class="sidebar-link">
                    <i class="fas fa-search-plus"></i>
                    <span>{% trans 'Advanced Search' %}</span>
                </a>
                
                <hr>
                
                <a href="{% url 'profiles:edit' %}" class="sidebar-link">
                    <i class="fas fa-cog"></i>
                    <span>{% trans 'Settings' %}</span>
                </a>
            </div>
            
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="card mt-3">
        <div class="card-body">
            <h6 class="card-title">{% trans 'Quick Actions' %}</h6>
            <div class="d-grid gap-2">
                <a href="{% url 'posts:create' %}" class="btn btn-yemeni-primary btn-sm">
                    <i class="fas fa-plus"></i> {% trans 'New Post' %}
                </a>
                <a href="{% url 'messaging:new_conversation' %}" class="btn btn-outline-yemeni-primary btn-sm">
                    <i class="fas fa-paper-plane"></i> {% trans 'New Message' %}
                </a>
            </div>
        </div>
    </div>
</div>
