from django.contrib import admin
from .models import Follow, FollowRequest, Block


@admin.register(Follow)
class FollowAdmin(admin.ModelAdmin):
    list_display = ('follower', 'following', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('follower__username', 'following__username')
    ordering = ('-created_at',)


@admin.register(FollowRequest)
class FollowRequestAdmin(admin.ModelAdmin):
    list_display = ('from_user', 'to_user', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('from_user__username', 'to_user__username')
    ordering = ('-created_at',)
    
    actions = ['accept_requests', 'reject_requests']
    
    def accept_requests(self, request, queryset):
        queryset.update(status='accepted')
        self.message_user(request, f'{queryset.count()} requests accepted.')
    accept_requests.short_description = 'Accept selected requests'
    
    def reject_requests(self, request, queryset):
        queryset.update(status='rejected')
        self.message_user(request, f'{queryset.count()} requests rejected.')
    reject_requests.short_description = 'Reject selected requests'


@admin.register(Block)
class BlockAdmin(admin.ModelAdmin):
    list_display = ('blocker', 'blocked', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('blocker__username', 'blocked__username')
    ordering = ('-created_at',)
