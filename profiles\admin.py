from django.contrib import admin
from .models import Profile


@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'location', 'is_private', 'followers_count', 'following_count', 'posts_count', 'created_at')
    list_filter = ('is_private', 'created_at')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'location')
    readonly_fields = ('followers_count', 'following_count', 'posts_count', 'created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('user', 'bio', 'location', 'website')
        }),
        ('Images', {
            'fields': ('profile_image', 'cover_image')
        }),
        ('Privacy', {
            'fields': ('is_private',)
        }),
        ('Statistics', {
            'fields': ('followers_count', 'following_count', 'posts_count'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
