# Athtar Deployment Guide

This guide covers deploying the Athtar social media platform to production.

## Pre-deployment Checklist

### Security
- [ ] Set `DEBUG = False` in production
- [ ] Use environment variables for sensitive data
- [ ] Generate a strong `SECRET_KEY`
- [ ] Configure HTTPS
- [ ] Set up proper CORS headers
- [ ] Configure secure cookies
- [ ] Set up rate limiting

### Database
- [ ] Use PostgreSQL for production
- [ ] Set up database backups
- [ ] Configure connection pooling
- [ ] Optimize database indexes

### Static Files & Media
- [ ] Configure static file serving
- [ ] Set up media file storage (AWS S3, etc.)
- [ ] Enable file compression
- [ ] Set up CDN

### Performance
- [ ] Configure caching (Redis/Memcached)
- [ ] Set up database query optimization
- [ ] Enable gzip compression
- [ ] Configure proper logging

## Environment Configuration

### Production Settings

Create a `production.py` settings file:

```python
from .settings import *
import os

# Security
DEBUG = False
ALLOWED_HOSTS = ['yourdomain.com', 'www.yourdomain.com']

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME'),
        'USER': os.environ.get('DB_USER'),
        'PASSWORD': os.environ.get('DB_PASSWORD'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
    }
}

# Security Settings
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Session Security
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# Static Files
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Media Files (AWS S3 example)
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = os.environ.get('AWS_STORAGE_BUCKET_NAME')
AWS_S3_REGION_NAME = os.environ.get('AWS_S3_REGION_NAME')

# Email
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')

# Caching
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### Environment Variables

```bash
# Database
DB_NAME=athtar_production
DB_USER=athtar_user
DB_PASSWORD=secure_password
DB_HOST=localhost
DB_PORT=5432

# Django
SECRET_KEY=your-super-secret-key
DEBUG=False
DJANGO_SETTINGS_MODULE=athtar.production

# Redis
REDIS_URL=redis://localhost:6379/0

# AWS S3 (if using)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_STORAGE_BUCKET_NAME=athtar-media
AWS_S3_REGION_NAME=us-east-1

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

## Deployment Options

### Option 1: Traditional VPS/Server

#### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install python3 python3-pip python3-venv nginx postgresql postgresql-contrib redis-server

# Create user
sudo adduser athtar
sudo usermod -aG sudo athtar
```

#### 2. Database Setup
```bash
# PostgreSQL setup
sudo -u postgres psql
CREATE DATABASE athtar_production;
CREATE USER athtar_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE athtar_production TO athtar_user;
\q
```

#### 3. Application Setup
```bash
# Clone repository
git clone <your-repo-url> /home/<USER>/athtar
cd /home/<USER>/athtar

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install gunicorn psycopg2-binary

# Set up environment
cp .env.example .env
# Edit .env with production values

# Run migrations
python manage.py migrate
python manage.py collectstatic --noinput
python manage.py createsuperuser
```

#### 4. Gunicorn Setup
Create `/etc/systemd/system/athtar.service`:

```ini
[Unit]
Description=Athtar Django Application
After=network.target

[Service]
User=athtar
Group=athtar
WorkingDirectory=/home/<USER>/athtar
Environment="PATH=/home/<USER>/athtar/venv/bin"
ExecStart=/home/<USER>/athtar/venv/bin/gunicorn --workers 3 --bind unix:/home/<USER>/athtar/athtar.sock athtar.wsgi:application
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
sudo systemctl daemon-reload
sudo systemctl start athtar
sudo systemctl enable athtar
```

#### 5. Nginx Setup
Create `/etc/nginx/sites-available/athtar`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    location = /favicon.ico { access_log off; log_not_found off; }
    
    location /static/ {
        root /home/<USER>/athtar;
    }
    
    location /media/ {
        root /home/<USER>/athtar;
    }

    location / {
        include proxy_params;
        proxy_pass http://unix:/home/<USER>/athtar/athtar.sock;
    }
}
```

```bash
sudo ln -s /etc/nginx/sites-available/athtar /etc/nginx/sites-enabled
sudo nginx -t
sudo systemctl restart nginx
```

#### 6. SSL Certificate (Let's Encrypt)
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### Option 2: Docker Deployment

#### Dockerfile
```dockerfile
FROM python:3.9

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

RUN python manage.py collectstatic --noinput

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "athtar.wsgi:application"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=************************************/athtar
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./media:/app/media

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=athtar
      - POSTGRES_USER=athtar
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine

volumes:
  postgres_data:
```

### Option 3: Cloud Deployment (Heroku)

#### Procfile
```
web: gunicorn athtar.wsgi:application
worker: python manage.py runworker
```

#### runtime.txt
```
python-3.9.16
```

#### Heroku Setup
```bash
# Install Heroku CLI
# Create Heroku app
heroku create athtar-app

# Add PostgreSQL
heroku addons:create heroku-postgresql:hobby-dev

# Add Redis
heroku addons:create heroku-redis:hobby-dev

# Set environment variables
heroku config:set SECRET_KEY=your-secret-key
heroku config:set DEBUG=False
heroku config:set DJANGO_SETTINGS_MODULE=athtar.production

# Deploy
git push heroku main

# Run migrations
heroku run python manage.py migrate
heroku run python manage.py createsuperuser
```

## Post-Deployment

### Monitoring
- Set up application monitoring (Sentry, New Relic)
- Configure server monitoring (Prometheus, Grafana)
- Set up log aggregation (ELK stack)

### Backups
- Database backups (automated daily)
- Media file backups
- Code repository backups

### Performance Optimization
- Enable database query optimization
- Set up caching strategies
- Configure CDN for static files
- Implement rate limiting

### Security
- Regular security updates
- SSL certificate renewal
- Security scanning
- Access log monitoring

## Maintenance

### Regular Tasks
- Update dependencies
- Monitor performance
- Review logs
- Database maintenance
- Security patches

### Scaling
- Horizontal scaling with load balancers
- Database read replicas
- Caching layers
- CDN optimization

## Troubleshooting

### Common Issues
1. **Static files not loading**: Check STATIC_ROOT and collectstatic
2. **Database connection errors**: Verify database credentials
3. **Permission errors**: Check file permissions and user ownership
4. **Memory issues**: Monitor memory usage and optimize queries
5. **SSL certificate issues**: Check certificate renewal

### Logs
- Application logs: `/var/log/athtar/`
- Nginx logs: `/var/log/nginx/`
- PostgreSQL logs: `/var/log/postgresql/`
- System logs: `/var/log/syslog`

## Support

For deployment support:
- Check the main README.md
- Review Django deployment documentation
- Contact the development team
- Create an issue on GitHub

---

**Happy Deploying! 🚀**
