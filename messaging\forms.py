from django import forms
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from .models import Message

User = get_user_model()


class MessageForm(forms.ModelForm):
    """Form for sending messages"""
    
    class Meta:
        model = Message
        fields = ('content',)
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Type your message...'),
                'required': True
            })
        }
        labels = {
            'content': ''
        }
    
    def clean_content(self):
        content = self.cleaned_data.get('content')
        if not content or not content.strip():
            raise forms.ValidationError(_('Message cannot be empty.'))
        return content.strip()


class NewConversationForm(forms.Form):
    """Form for starting a new conversation"""
    
    recipient = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter username'),
            'required': True
        }),
        label=_('Recipient')
    )
    message = forms.CharField(
        max_length=1000,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': _('Type your message...'),
            'required': True
        }),
        label=_('Message')
    )
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
    
    def clean_recipient(self):
        username = self.cleaned_data.get('recipient')
        try:
            recipient = User.objects.get(username=username)
        except User.DoesNotExist:
            raise forms.ValidationError(_('User with this username does not exist.'))
        
        if recipient == self.user:
            raise forms.ValidationError(_('You cannot send a message to yourself.'))
        
        # Check if user is blocked
        from social.models import Block
        if Block.objects.filter(blocker=recipient, blocked=self.user).exists():
            raise forms.ValidationError(_('You cannot send a message to this user.'))
        
        return recipient
    
    def clean_message(self):
        message = self.cleaned_data.get('message')
        if not message or not message.strip():
            raise forms.ValidationError(_('Message cannot be empty.'))
        return message.strip()
