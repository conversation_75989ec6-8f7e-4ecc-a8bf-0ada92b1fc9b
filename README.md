# عثتر (Athtar) - Yemeni Social Media Platform

A complete social media platform built with Django, designed specifically for the Yemeni community with Arabic language support and cultural elements.

## Features

### Core Features
- ✅ User registration and authentication
- ✅ User profiles with profile pictures and bio
- ✅ Post creation (text, images, videos)
- ✅ News feed/timeline showing posts from followed users
- ✅ Like, comment, and share functionality
- ✅ Follow/unfollow users
- ✅ Direct messaging system
- ✅ Search functionality for users and posts

### Yemeni Cultural Elements
- ✅ Arabic language support (RTL layout)
- ✅ Yemeni-themed design and colors
- ✅ Cultural content categories and hashtags
- ✅ Local timezone support (Asia/Aden)
- ✅ Traditional Yemeni color palette

### Technical Features
- ✅ Django framework with Python
- ✅ Proper database models using Django ORM
- ✅ Responsive web interface
- ✅ User authentication and authorization
- ✅ URL routing and views
- ✅ Media file handling
- ✅ Security measures (CSRF protection, input validation)
- ✅ Real-time messaging with WebSockets
- ✅ Bootstrap 5 RTL support

## Installation

### Prerequisites
- Python 3.8+
- pip
- Virtual environment (recommended)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd athtar
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment Configuration**
   ```bash
   # Copy the example environment file
   cp .env.example .env
   
   # Edit .env file with your settings
   # Generate a secret key: python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
   ```

5. **Database Setup**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

6. **Create Superuser**
   ```bash
   python manage.py createsuperuser
   ```

7. **Collect Static Files**
   ```bash
   python manage.py collectstatic
   ```

8. **Run Development Server**
   ```bash
   python manage.py runserver
   ```

9. **Access the Application**
   - Open your browser and go to `http://127.0.0.1:8000`
   - Admin panel: `http://127.0.0.1:8000/admin`

## Project Structure

```
athtar/
├── athtar/                 # Main project directory
│   ├── settings.py        # Django settings
│   ├── urls.py           # Main URL configuration
│   ├── wsgi.py           # WSGI configuration
│   └── asgi.py           # ASGI configuration for WebSockets
├── accounts/              # User authentication app
├── profiles/              # User profiles app
├── posts/                 # Posts and content app
├── social/                # Social features (follow/unfollow)
├── messaging/             # Direct messaging app
├── search/                # Search functionality app
├── templates/             # HTML templates
├── static/                # Static files (CSS, JS, images)
├── media/                 # User uploaded files
├── requirements.txt       # Python dependencies
└── manage.py             # Django management script
```

## Apps Overview

### Accounts App
- Custom user model with email authentication
- Registration and login views
- User profile management

### Profiles App
- Extended user profiles with bio, images
- Profile statistics (followers, following, posts)
- Privacy settings

### Posts App
- Create, read, update, delete posts
- Support for text, images, and videos
- Like and comment system
- Share functionality

### Social App
- Follow/unfollow relationships
- Follow requests for private accounts
- Block functionality
- Suggested users

### Messaging App
- Direct messaging between users
- Real-time chat with WebSockets
- Conversation management

### Search App
- Search users and posts
- Advanced search with filters
- Hashtag search

## Configuration

### Environment Variables
Create a `.env` file with the following variables:

```env
SECRET_KEY=your-secret-key-here
DEBUG=True
DATABASE_URL=sqlite:///db.sqlite3
REDIS_URL=redis://localhost:6379/0
```

### Database
The project uses SQLite by default. For production, configure PostgreSQL:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'athtar_db',
        'USER': 'your_username',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

### Redis (for WebSockets)
Install and run Redis for real-time messaging:

```bash
# On Ubuntu/Debian
sudo apt-get install redis-server

# On macOS
brew install redis

# Start Redis
redis-server
```

## Deployment

### Production Settings
1. Set `DEBUG=False` in production
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving (WhiteNoise included)
4. Configure email backend for notifications
5. Set up Redis for WebSocket support
6. Use environment variables for sensitive data

### Security Checklist
- ✅ CSRF protection enabled
- ✅ XSS protection
- ✅ SQL injection protection (Django ORM)
- ✅ Secure file upload handling
- ✅ Input validation and sanitization
- ✅ Authentication and authorization

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Cultural Considerations

This platform is designed with Yemeni culture in mind:

- **Language**: Primary language is Arabic with RTL support
- **Design**: Uses traditional Yemeni colors and patterns
- **Content**: Supports cultural hashtags and local content
- **Time**: Uses Yemen timezone (Asia/Aden)
- **Community**: Features promote Yemeni cultural values

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

## Acknowledgments

- Built with Django and Bootstrap
- Inspired by Yemeni culture and heritage
- Thanks to the open-source community

---

**عثتر - ربط اليمن بالعالم** 🇾🇪
