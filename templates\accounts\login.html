{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans 'Login' %} - عثتر{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        
        <!-- Logo and Welcome -->
        <div class="text-center mb-4">
            <img src="{% static 'images/logo.png' %}" alt="عثتر" height="80" class="mb-3">
            <h2 class="text-yemeni-primary">مرحباً بك في عثتر</h2>
            <p class="text-muted">{% trans 'Connect with Yemenis around the world' %}</p>
        </div>
        
        <!-- Login Form -->
        <div class="card shadow">
            <div class="card-body p-4">
                <h4 class="card-title text-center mb-4">{% trans 'Login' %}</h4>
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            {{ form.username.label }}
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small">
                                {{ form.username.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            {{ form.password.label }}
                        </label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="text-danger small">
                                {{ form.password.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-yemeni-primary btn-lg">
                            {% trans 'Login' %}
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="mb-0">
                        {% trans "Don't have an account?" %}
                        <a href="{% url 'accounts:register' %}" class="text-yemeni-primary">
                            {% trans 'Register now' %}
                        </a>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Cultural Elements -->
        <div class="text-center mt-4">
            <div class="yemeni-pattern">
                <span class="badge bg-yemeni-primary me-2">🇾🇪</span>
                <span class="text-muted">{% trans 'Proudly Yemeni' %}</span>
                <span class="badge bg-yemeni-primary ms-2">☕</span>
            </div>
        </div>
        
    </div>
</div>
{% endblock %}
