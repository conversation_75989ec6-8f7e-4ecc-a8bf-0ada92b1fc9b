from django.urls import path
from . import views

app_name = 'social'

urlpatterns = [
    path('follow/<str:username>/', views.toggle_follow, name='toggle_follow'),
    path('requests/', views.follow_requests, name='follow_requests'),
    path('requests/<int:request_id>/', views.handle_follow_request, name='handle_follow_request'),
    path('block/<str:username>/', views.block_user, name='block_user'),
    path('unblock/<str:username>/', views.unblock_user, name='unblock_user'),
    path('blocked/', views.blocked_users, name='blocked_users'),
    path('suggested/', views.suggested_users, name='suggested_users'),
]
