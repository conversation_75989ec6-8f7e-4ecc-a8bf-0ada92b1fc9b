{% load static %}
{% load i18n %}
{% load humanize %}

<div class="card post-card" data-post-id="{{ post.id }}">
    
    <!-- Post Header -->
    <div class="post-header">
        <div class="d-flex align-items-center">
            <a href="{% url 'profiles:profile' username=post.author.username %}">
                <img src="{{ post.author.profile.profile_image.url }}" alt="{{ post.author.get_full_name }}" 
                     class="rounded-circle me-3" width="45" height="45">
            </a>
            <div class="flex-grow-1">
                <div class="d-flex align-items-center">
                    <a href="{% url 'profiles:profile' username=post.author.username %}" 
                       class="text-decoration-none text-dark">
                        <strong>{{ post.author.get_full_name }}</strong>
                    </a>
                    {% if post.author.profile.is_verified %}
                        <i class="fas fa-check-circle text-yemeni-primary ms-1" 
                           data-bs-toggle="tooltip" title="{% trans 'Verified Account' %}"></i>
                    {% endif %}
                </div>
                <div class="text-muted small">
                    <a href="{% url 'profiles:profile' username=post.author.username %}" 
                       class="text-muted text-decoration-none">@{{ post.author.username }}</a>
                    <span class="mx-1">•</span>
                    <a href="{% url 'posts:detail' pk=post.id %}" 
                       class="text-muted text-decoration-none">
                        {{ post.created_at|naturaltime }}
                    </a>
                </div>
            </div>
            
            <!-- Post Options -->
            {% if user == post.author %}
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                            type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="{% url 'posts:update' pk=post.id %}">
                                <i class="fas fa-edit me-2"></i>{% trans 'Edit' %}
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item text-danger" href="{% url 'posts:delete' pk=post.id %}">
                                <i class="fas fa-trash me-2"></i>{% trans 'Delete' %}
                            </a>
                        </li>
                    </ul>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Post Content -->
    <div class="post-content">
        <p class="mb-2">{{ post.content|linebreaks }}</p>
    </div>
    
    <!-- Post Media -->
    {% if post.image %}
        <div class="post-media">
            <img src="{{ post.image.url }}" alt="Post image" class="img-fluid">
        </div>
    {% elif post.video %}
        <div class="post-media">
            <video controls class="w-100">
                <source src="{{ post.video.url }}" type="video/mp4">
                {% trans 'Your browser does not support the video tag.' %}
            </video>
        </div>
    {% endif %}
    
    <!-- Post Actions -->
    <div class="post-actions">
        <div class="d-flex justify-content-between align-items-center">
            <div class="action-buttons">
                
                <!-- Like Button -->
                <button class="post-action-btn like-btn {% if post.is_liked_by:user %}liked{% endif %}" 
                        data-post-id="{{ post.id }}">
                    <i class="{% if post.is_liked_by:user %}fas{% else %}far{% endif %} fa-heart"></i>
                    <span class="like-count">{{ post.likes_count }}</span>
                </button>
                
                <!-- Comment Button -->
                <button class="post-action-btn" onclick="toggleComments({{ post.id }})">
                    <i class="far fa-comment"></i>
                    <span>{{ post.comments_count }}</span>
                </button>
                
                <!-- Share Button -->
                <button class="post-action-btn share-btn" data-post-id="{{ post.id }}">
                    <i class="fas fa-share"></i>
                    <span class="share-count">{{ post.shares_count }}</span>
                </button>
                
            </div>
            
            <!-- Bookmark -->
            <button class="post-action-btn">
                <i class="far fa-bookmark"></i>
            </button>
        </div>
        
        <!-- Comments Section -->
        <div class="comments-section mt-3" id="comments-{{ post.id }}" style="display: none;">
            
            <!-- Add Comment Form -->
            {% if user.is_authenticated %}
                <form method="post" action="{% url 'posts:add_comment' post_id=post.id %}" class="mb-3">
                    {% csrf_token %}
                    <div class="d-flex">
                        <img src="{{ user.profile.profile_image.url }}" alt="{{ user.get_full_name }}" 
                             class="rounded-circle me-2" width="32" height="32">
                        <div class="flex-grow-1">
                            <textarea name="content" class="form-control form-control-sm" 
                                    placeholder="{% trans 'Write a comment...' %}" rows="2" required></textarea>
                            <div class="text-end mt-2">
                                <button type="submit" class="btn btn-yemeni-primary btn-sm">
                                    {% trans 'Comment' %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            {% endif %}
            
            <!-- Comments List -->
            <div class="comments-list">
                {% for comment in post.get_comments|slice:":3" %}
                    {% include 'posts/includes/comment_item.html' %}
                {% endfor %}
                
                {% if post.comments_count > 3 %}
                    <div class="text-center mt-2">
                        <a href="{% url 'posts:detail' pk=post.id %}" class="btn btn-link btn-sm">
                            {% trans 'View all' %} {{ post.comments_count }} {% trans 'comments' %}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function toggleComments(postId) {
    const commentsSection = document.getElementById('comments-' + postId);
    if (commentsSection.style.display === 'none') {
        commentsSection.style.display = 'block';
    } else {
        commentsSection.style.display = 'none';
    }
}
</script>
