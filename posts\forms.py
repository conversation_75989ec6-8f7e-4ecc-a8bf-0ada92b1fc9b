from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Post, Comment


class PostCreateForm(forms.ModelForm):
    """Form for creating new posts"""
    
    class Meta:
        model = Post
        fields = ('content', 'image', 'video')
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': _('What\'s on your mind?'),
                'required': True
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'video': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'video/*'
            })
        }
        labels = {
            'content': _('Content'),
            'image': _('Image'),
            'video': _('Video')
        }
        help_texts = {
            'content': _('Maximum 2000 characters'),
            'image': _('Maximum file size: 5MB'),
            'video': _('Maximum file size: 50MB')
        }
    
    def clean_content(self):
        content = self.cleaned_data.get('content')
        if not content or not content.strip():
            raise forms.ValidationError(_('Content cannot be empty.'))
        return content.strip()
    
    def clean_image(self):
        image = self.cleaned_data.get('image')
        if image:
            if image.size > 5 * 1024 * 1024:  # 5MB
                raise forms.ValidationError(_('Image file too large ( > 5MB )'))
        return image
    
    def clean_video(self):
        video = self.cleaned_data.get('video')
        if video:
            if video.size > 50 * 1024 * 1024:  # 50MB
                raise forms.ValidationError(_('Video file too large ( > 50MB )'))
        return video
    
    def clean(self):
        cleaned_data = super().clean()
        image = cleaned_data.get('image')
        video = cleaned_data.get('video')
        
        if image and video:
            raise forms.ValidationError(_('You can only upload either an image or a video, not both.'))
        
        return cleaned_data


class PostUpdateForm(forms.ModelForm):
    """Form for updating existing posts"""
    
    class Meta:
        model = Post
        fields = ('content',)
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4
            })
        }
        labels = {
            'content': _('Content')
        }
    
    def clean_content(self):
        content = self.cleaned_data.get('content')
        if not content or not content.strip():
            raise forms.ValidationError(_('Content cannot be empty.'))
        return content.strip()


class CommentForm(forms.ModelForm):
    """Form for creating comments"""
    
    class Meta:
        model = Comment
        fields = ('content',)
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': _('Write a comment...'),
                'required': True
            })
        }
        labels = {
            'content': ''
        }
    
    def clean_content(self):
        content = self.cleaned_data.get('content')
        if not content or not content.strip():
            raise forms.ValidationError(_('Comment cannot be empty.'))
        return content.strip()
