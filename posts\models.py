from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
import os

# Try to import PIL, but don't fail if it's not available
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

User = get_user_model()


def post_media_path(instance, filename):
    """Generate file path for post media"""
    return f'posts/{instance.author.username}/{filename}'


class Post(models.Model):
    """Post model for social media content"""
    
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='posts')
    content = models.TextField(_('content'), max_length=2000)
    image = models.ImageField(_('image'), upload_to=post_media_path, blank=True, null=True)
    video = models.FileField(_('video'), upload_to=post_media_path, blank=True, null=True)
    likes_count = models.PositiveIntegerField(_('likes count'), default=0)
    comments_count = models.PositiveIntegerField(_('comments count'), default=0)
    shares_count = models.PositiveIntegerField(_('shares count'), default=0)
    is_pinned = models.BooleanField(_('pinned'), default=False)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Post')
        verbose_name_plural = _('Posts')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['-created_at']),
            models.Index(fields=['author', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.author.username}: {self.content[:50]}..."
    
    def get_absolute_url(self):
        return reverse('posts:detail', kwargs={'pk': self.pk})
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Resize image if it exists and PIL is available
        if PIL_AVAILABLE and self.image and hasattr(self.image, 'path'):
            try:
                img = Image.open(self.image.path)
                if img.height > 800 or img.width > 800:
                    output_size = (800, 800)
                    img.thumbnail(output_size)
                    img.save(self.image.path)
            except Exception:
                pass
    
    def get_likes(self):
        """Get all likes for this post"""
        return self.likes.all()
    
    def get_comments(self):
        """Get all comments for this post"""
        return self.comments.filter(parent=None).order_by('-created_at')
    
    def is_liked_by(self, user):
        """Check if post is liked by user"""
        if user.is_authenticated:
            return self.likes.filter(user=user).exists()
        return False


class Comment(models.Model):
    """Comment model for posts"""
    
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comments')
    content = models.TextField(_('content'), max_length=500)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')
    likes_count = models.PositiveIntegerField(_('likes count'), default=0)
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Comment')
        verbose_name_plural = _('Comments')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.author.username}: {self.content[:30]}..."
    
    def get_replies(self):
        """Get all replies to this comment"""
        return self.replies.all().order_by('created_at')
    
    def is_liked_by(self, user):
        """Check if comment is liked by user"""
        if user.is_authenticated:
            return self.comment_likes.filter(user=user).exists()
        return False


class Like(models.Model):
    """Like model for posts"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='likes')
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='likes')
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Like')
        verbose_name_plural = _('Likes')
        unique_together = ('user', 'post')
        indexes = [
            models.Index(fields=['post', '-created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} likes {self.post.id}"


class CommentLike(models.Model):
    """Like model for comments"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comment_likes')
    comment = models.ForeignKey(Comment, on_delete=models.CASCADE, related_name='comment_likes')
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Comment Like')
        verbose_name_plural = _('Comment Likes')
        unique_together = ('user', 'comment')
    
    def __str__(self):
        return f"{self.user.username} likes comment {self.comment.id}"


class Share(models.Model):
    """Share model for posts"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='shares')
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='shares')
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Share')
        verbose_name_plural = _('Shares')
        unique_together = ('user', 'post')
    
    def __str__(self):
        return f"{self.user.username} shared {self.post.id}"


# Signals to update counts
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

@receiver(post_save, sender=Like)
def increment_post_likes(sender, instance, created, **kwargs):
    if created:
        instance.post.likes_count += 1
        instance.post.save(update_fields=['likes_count'])

@receiver(post_delete, sender=Like)
def decrement_post_likes(sender, instance, **kwargs):
    instance.post.likes_count -= 1
    instance.post.save(update_fields=['likes_count'])

@receiver(post_save, sender=Comment)
def increment_post_comments(sender, instance, created, **kwargs):
    if created:
        instance.post.comments_count += 1
        instance.post.save(update_fields=['comments_count'])

@receiver(post_delete, sender=Comment)
def decrement_post_comments(sender, instance, **kwargs):
    instance.post.comments_count -= 1
    instance.post.save(update_fields=['comments_count'])

@receiver(post_save, sender=Share)
def increment_post_shares(sender, instance, created, **kwargs):
    if created:
        instance.post.shares_count += 1
        instance.post.save(update_fields=['shares_count'])

@receiver(post_delete, sender=Share)
def decrement_post_shares(sender, instance, **kwargs):
    instance.post.shares_count -= 1
    instance.post.save(update_fields=['shares_count'])

@receiver(post_save, sender=CommentLike)
def increment_comment_likes(sender, instance, created, **kwargs):
    if created:
        instance.comment.likes_count += 1
        instance.comment.save(update_fields=['likes_count'])

@receiver(post_delete, sender=CommentLike)
def decrement_comment_likes(sender, instance, **kwargs):
    instance.comment.likes_count -= 1
    instance.comment.save(update_fields=['likes_count'])

@receiver(post_save, sender=Post)
def increment_user_posts(sender, instance, created, **kwargs):
    if created:
        profile = instance.author.profile
        profile.posts_count += 1
        profile.save(update_fields=['posts_count'])

@receiver(post_delete, sender=Post)
def decrement_user_posts(sender, instance, **kwargs):
    profile = instance.author.profile
    profile.posts_count -= 1
    profile.save(update_fields=['posts_count'])
